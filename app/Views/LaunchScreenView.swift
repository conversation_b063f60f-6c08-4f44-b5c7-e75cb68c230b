//
//  LaunchScreenView.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 24..
//

import SwiftUI

struct LaunchScreenView: View {
    var body: some View {
        ZStack {
            // Use habit background color
            Color.habitBackground
                .ignoresSafeArea()
            
            VStack(spacing: 24) {
                // App icon placeholder
                Image(systemName: "bolt.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.habitPurple)
                    .shadow(color: Color.habitPurple.opacity(0.3), radius: 10)
                
                Text("LightningHabit")
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(.habitTextPrimary)
                
                // Loading indicator
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .habitPurple))
                    .scaleEffect(0.8)
            }
        }
    }
}

#Preview {
    LaunchScreenView()
}